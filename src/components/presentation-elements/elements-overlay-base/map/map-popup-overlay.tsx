import { useMap } from '@vis.gl/react-google-maps';
import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';

interface CustomPopupOverlayProps {
	position: google.maps.LatLngLiteral;
	content: string | React.ReactNode;
	minZoom?: number;
	placement?: 'top' | 'bottom' | 'left' | 'right';
	isBlackTheme?: boolean;
}

const MapPopupOverlay: React.FC<CustomPopupOverlayProps> = ({ position, content, isBlackTheme }) => {
	const mapInstance = useMap();

	const mapOverlayRef = useRef<google.maps.OverlayView | null>(null);
	const [isMapPopupVisible, setMapPopupVisible] = useState(true);

	useEffect(() => {
		if (!mapInstance) return;

		setMapPopupVisible(true);
	}, [mapInstance, content]);

	useEffect(() => {
		if (!mapInstance || !isMapPopupVisible || typeof mapInstance.getZoom !== 'function') return;

		class LocationTooltip extends google.maps.OverlayView {
			popupPosition: google.maps.LatLng;
			popupContainer: HTMLDivElement;
			reactRoot: any;
			clickHandler?: () => void;

			constructor(pos: google.maps.LatLng, innerContent: string | React.ReactNode) {
				super();

				this.popupPosition = pos;

				this.popupContainer = document.createElement('div');
				this.popupContainer.classList.add(...['absolute', 'w-auto', 'h-auto', 'overflow-visible', 'pointer-events-auto']);
				this.popupContainer.style.zIndex = '10';

				const contentContainer = document.createElement('div');
				contentContainer.classList.add(
					...[
						'bg-gradient-to-br',
						'from-white',
						'to-gray-50',
						'dark:from-gray-800',
						'dark:to-gray-900',
						'rounded-xl',
						'px-4',
						'py-3',
						'text-sm',

						isBlackTheme ? 'text-white' : 'text-gray-900',
						'shadow-2xl',
						'shadow-black/20',
						'dark:shadow-black/40',
						'border',
						'border-gray-200/50',
						'dark:border-gray-700/50',
						'backdrop-blur-sm',
						'max-w-xs',
						'transform',
						'-translate-x-1/2',
						'translate-y-3',
						'relative',
						'transition-all',
						'duration-300',
						'ease-out',
						'hover:scale-105',
						'hover:shadow-3xl',
						'hover:shadow-blue-500/20',
						'dark:hover:shadow-blue-400/20',
						'animate-in',
						'fade-in-0',
						'zoom-in-95',
						'slide-in-from-bottom-2',
					],
				);

				contentContainer.style.background = `
					linear-gradient(135deg, 
						rgba(255, 255, 255, 0.95) 0%, 
						rgba(248, 250, 252, 0.95) 100%
					)
				`;

				if (isBlackTheme) {
					contentContainer.style.background = `
						linear-gradient(135deg, 
							rgba(31, 41, 55, 0.95) 0%, 
							rgba(17, 24, 39, 0.95) 100%
						)
					`;
				}

				const arrow = document.createElement('div');
				arrow.classList.add(...['absolute', 'bottom-full', 'left-1/2', 'transform', '-translate-x-1/2', 'filter', 'drop-shadow-sm']);
				arrow.innerHTML = `
					<div class="relative">
						<div class="w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-white dark:border-b-gray-800 filter drop-shadow-sm"></div>
						<div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-gradient-to-br border-b-from-white border-b-to-gray-50 dark:border-b-from-gray-800 dark:border-b-to-gray-900"></div>
					</div>
				`;
				contentContainer.appendChild(arrow);

				if (typeof innerContent === 'string') {
					const textContent = document.createElement('div');
					textContent.innerHTML = innerContent;
					textContent.classList.add('font-medium', 'leading-relaxed');
					contentContainer.insertBefore(textContent, contentContainer.firstChild);
				} else {
					const contentWrapper = document.createElement('div');
					contentWrapper.classList.add('font-medium', 'leading-relaxed');
					contentContainer.insertBefore(contentWrapper, contentContainer.firstChild);
					this.reactRoot = createRoot(contentWrapper);
					this.reactRoot.render(innerContent);
				}

				const iconElement = document.createElement('div');
				iconElement.classList.add('absolute', 'top-2', 'right-2', 'w-2', 'h-2', 'bg-primary', 'rounded-full', 'opacity-60');
				contentContainer.appendChild(iconElement);
				this.popupContainer.appendChild(contentContainer);
				LocationTooltip.preventMapHitsAndGesturesFrom(this.popupContainer);
			}

			onAdd() {
				this.getPanes()?.overlayLayer.appendChild(this.popupContainer);
			}

			onRemove() {
				if (this.reactRoot) {
					this.reactRoot.unmount();
				}
				if (this.popupContainer.parentElement) {
					this.popupContainer.parentElement.removeChild(this.popupContainer);
				}
			}

			draw() {
				const divPosition = this.getProjection().fromLatLngToDivPixel(this.popupPosition);

				if (!divPosition) return;
				const display = Math.abs(divPosition.x) < 4000 && Math.abs(divPosition.y) < 4000 ? 'block' : 'none';
				if (display === 'block') {
					this.popupContainer.style.left = `${divPosition.x}px`;
					this.popupContainer.style.top = `${divPosition.y}px`;

					if (this.popupContainer.style.opacity !== '1') {
						this.popupContainer.style.opacity = '0';
						this.popupContainer.style.transform = 'scale(0.8) translateY(10px)';

						setTimeout(() => {
							this.popupContainer.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
							this.popupContainer.style.opacity = '1';
							this.popupContainer.style.transform = 'scale(1) translateY(0)';
						}, 50);
					}
				}
				if (this.popupContainer.style.display !== display) {
					this.popupContainer.style.display = display;
				}
			}
		}

		let coordinates;
		try {
			coordinates = new google.maps.LatLng(position.lat, position.lng);
		} catch (error) {
			console.error('Falha ao criar o objeto LatLng para a posição do pop-up:', error);
			return;
		}

		if (coordinates) {
			const popup = new LocationTooltip(coordinates, content);

			if (mapOverlayRef.current) {
				mapOverlayRef.current.setMap(null);
			}

			mapOverlayRef.current = popup;
			popup.setMap(mapInstance);

			return () => {
				popup.setMap(null);
				mapOverlayRef.current = null;
			};
		}
	}, [mapInstance, position, content, isMapPopupVisible]);

	return null;
};

export default MapPopupOverlay;
