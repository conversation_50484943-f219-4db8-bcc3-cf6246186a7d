import { IImagePreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/image/image-editor.types';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue } from 'jotai';
import React from 'react';

interface ImagePreviewProps {
	id: string;
}

export const ImagePlaceholder: React.FC<{ message: string }> = ({ message }) => (
	<div className="flex h-full w-full flex-col items-center justify-center overflow-hidden p-2 text-gray-500">
		<div className="flex min-h-[60px] flex-col items-center justify-center text-center">
			<svg width="100%" height="100%" viewBox="0 0 24 24" className="mb-1 h-8 w-8 text-gray-400 sm:h-10 sm:w-10" fill="none">
				<path
					d="M21 19V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2ZM7 10a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 7H7l3.5-4.5 2.5 3 2-2.5 4 4Z"
					stroke="currentColor"
					strokeWidth={1.5}
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
			<span className="line-clamp-2 max-w-full break-words text-center text-xs font-medium sm:text-sm">{message}</span>
		</div>
	</div>
);

function getImageStyle(content?: IImagePreview): React.CSSProperties {
	if (!content) return {};
	const { opacity, border, backgroundColor, maintainAspectRatio } = content;
	return {
		width: maintainAspectRatio ? 'auto' : '100%',
		height: maintainAspectRatio ? '100%' : 'auto',
		opacity,
		border: border ? `${border.width}px ${border.style} ${border.color}` : undefined,
		backgroundColor,
	};
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({ id }) => {
	const items = useAtomValue(itemsAtom);
	const currentItem = items.find((item) => item.id === id);
	const content = currentItem?.content as IImagePreview | undefined;
	const imageUrl = currentItem?.media?.[0]?.url;
	if (!currentItem || !content) return <ImagePlaceholder message="Item não encontrado" />;
	if (!imageUrl) return <ImagePlaceholder message="Imagem não encontrada" />;
	return <img src={imageUrl} alt={currentItem.name || 'Preview da imagem'} style={getImageStyle(content)} className="h-full w-full object-contain" />;
};
