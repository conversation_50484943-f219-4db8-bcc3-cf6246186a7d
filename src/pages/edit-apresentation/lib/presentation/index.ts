import { v4 as uuidv4 } from 'uuid';
import { type IElement } from '../../states/presentation/presentation-info.state';
import { type IItem, type IItemShapeType, ITEM_STATUS } from '../../types/item.type';

const validateNumericValue = (value: unknown, defaultValue: number): number => {
	const num = Number(value);
	return isNaN(num) ? defaultValue : num;
};

const sortElementsByLayer = (elements: Readonly<IElement[]>): IElement[] => [...elements].sort((a, b) => b.layer - a.layer);

const transformElementToItem = (element: Readonly<IElement>): IItem => ({
	tempId: uuidv4(),
	id: element.id,
	status: ITEM_STATUS.SAVED,
	name: element.title,
	type: element.elementType as IItemShapeType,
	position: {
		x: validateNumericValue(element.positionX, 0),
		y: validateNumericValue(element.positionY, 0),
	},
	size: {
		width: validateNumericValue(element.width, 100),
		height: validateNumericValue(element.height, 100),
	},
	content: element.content,
	layer: element.layer,
	media: element.media,
});

export function mapElementsToItems(elements: Readonly<IElement[]>): IItem[] {
	return sortElementsByLayer(elements).map(transformElementToItem);
}
