import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { ITEM_STATUS } from '../../../types/item.type';

export const useCanvasLayers = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedItemsIds, setSelectedItemsIds] = useAtom(selectedItemsIdsAtom);

	const getSelectedItems = useCallback(() => {
		return items.filter((item) => selectedItemsIds.includes(item.tempId));
	}, [items, selectedItemsIds]);

	const handleBringToFront = useCallback(() => {
		const selectedItems = getSelectedItems();
		if (selectedItems.length === 0) return;

		const maxLayer = Math.max(...items.map((item) => item.layer));

		setItems(
			items.map((item) => {
				if (selectedItemsIds.includes(item.tempId)) {
					return { ...item, layer: maxLayer + 1 };
				}
				return item;
			}),
		);
	}, [items, setItems, selectedItemsIds, getSelectedItems]);

	const handleSendToBack = useCallback(() => {
		const selectedItems = getSelectedItems();
		if (selectedItems.length === 0) return;

		const minLayer = Math.min(...items.map((item) => item.layer));

		setItems(
			items.map((item) => {
				if (selectedItemsIds.includes(item.tempId)) {
					return { ...item, layer: minLayer - 1 };
				}
				return item;
			}),
		);
	}, [items, setItems, selectedItemsIds, getSelectedItems]);

	const handleBringForward = useCallback(() => {
		const selectedItems = getSelectedItems();
		if (selectedItems.length === 0) return;

		setItems(
			items.map((item) => {
				if (selectedItemsIds.includes(item.tempId)) {
					return { ...item, layer: item.layer + 1 };
				}
				return item;
			}),
		);
	}, [items, setItems, selectedItemsIds, getSelectedItems]);

	const handleSendBackward = useCallback(() => {
		const selectedItems = getSelectedItems();
		if (selectedItems.length === 0) return;

		setItems(
			items.map((item) => {
				if (selectedItemsIds.includes(item.tempId)) {
					return { ...item, layer: Math.max(1, item.layer - 1) };
				}
				return item;
			}),
		);
	}, [items, setItems, selectedItemsIds, getSelectedItems]);

	const handleDuplicate = useCallback(() => {
		const selectedItems = getSelectedItems();
		if (selectedItems.length === 0) {
			toast.warning('Nenhum elemento selecionado para duplicar');
			return;
		}

		const maxLayer = Math.max(...items.map((item) => item.layer));

		const duplicatedItems = selectedItems.map((item, index) => ({
			...item,
			tempId: uuidv4(),
			layer: maxLayer + index + 1,
			status: ITEM_STATUS.NEW,
			position: {
				x: item.position.x + 20,
				y: item.position.y + 20,
			},
		}));

		setItems([...items, ...duplicatedItems]);
		setSelectedItemsIds(duplicatedItems.map((item) => item.tempId));
		toast.success(`${duplicatedItems.length} elemento(s) duplicado(s)`);
	}, [items, setItems, setSelectedItemsIds, getSelectedItems]);

	return {
		handleBringToFront,
		handleSendToBack,
		handleBringForward,
		handleSendBackward,
		handleDuplicate,
	};
};
