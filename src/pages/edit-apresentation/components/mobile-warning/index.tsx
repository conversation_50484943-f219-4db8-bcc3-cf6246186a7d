import { motion } from 'framer-motion';
import Particles from '@/components/shadcnui/particles';
import { useNavigate } from 'react-router-dom';

export const MobileWarning = () => {
	const router = useNavigate();

	return (
		<main className="relative flex h-full min-h-screen w-full overflow-hidden bg-gradient-to-br from-background via-background/95 to-muted/30">
			<div className="pointer-events-none absolute inset-0 z-0">
				<div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/50 to-background/20" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.15),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(34,197,94,0.1),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.08),transparent_40%)]" />
				<Particles className="absolute inset-0" quantity={100} />
			</div>

			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.8 }}
				className="relative z-10 flex h-full w-full items-center justify-center p-6"
			>
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					transition={{ duration: 0.5 }}
					className="flex max-w-md flex-col items-center justify-center text-center"
				>
					<motion.svg
						xmlns="http://www.w3.org/2000/svg"
						className="mb-4 h-20 w-20 text-primary"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
						initial={{ scale: 0 }}
						animate={{ scale: 1 }}
						transition={{ duration: 0.5 }}
					>
						<motion.path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
							initial={{ pathLength: 0 }}
							animate={{ pathLength: 1 }}
							transition={{ duration: 1 }}
						/>
					</motion.svg>
					<motion.h2
						className="mb-2 text-2xl font-bold text-gray-100 [text-shadow:0_0_10px_rgba(34,197,94,0.5)]"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.3, duration: 0.5 }}
					>
						Dispositivo não compatível
					</motion.h2>
					<motion.p className="text-gray-400/80" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.5, duration: 0.5 }}>
						Esta funcionalidade está disponível apenas em dispositivos desktop. Por favor, acesse novamente em um computador.
					</motion.p>

					<>
						<button
							onClick={() => router('/')}
							className="mt-6 flex items-center gap-2 rounded-lg bg-gradient-to-r from-primary/30 via-primary/40 to-primary/50 px-8 py-3 font-semibold text-white shadow-lg transition-all [text-shadow:0_0_5px_rgba(0,0,0,0.3)] hover:from-green-600 hover:via-primary/90 hover:to-primary/70 hover:shadow-primary/40"
						>
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0h6"
								/>
							</svg>
							Tela Inicial
						</button>
					</>
				</motion.div>
			</motion.div>

			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.5 }}
				className="pointer-events-none absolute bottom-0 left-0 h-px w-full bg-gradient-to-r from-transparent via-primary/20 to-transparent"
			/>
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.7 }}
				className="pointer-events-none absolute right-0 top-0 h-full w-px bg-gradient-to-b from-transparent via-primary/10 to-transparent"
			/>
		</main>
	);
};
