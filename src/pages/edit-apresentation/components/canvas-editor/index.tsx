'use client';

import { ErrorBoundary } from '@/components/error-boundary/error-boundary';
import { HTMLOverlay } from '@/components/presentation-elements/html-overlay/html-overlay';
import { OverlayContainer } from '@/components/presentation-elements/html-overlay/overlay-container';
import { useAtomValue } from 'jotai';
import { memo, useMemo } from 'react';
import { nextElementAtom } from '../../../../shared/states/items/next-items.state';
import { itemsAtom } from '../../../../shared/states/items/object-item.state';
import { useCanvasEditor } from '../../hooks/canvas/config';
import { useSelectionBox, useShapeHover } from '../../hooks/canvas/interactions';
import { useClickOutside } from '../../hooks/canvas/interactions/events/click-outside.hook';
import { useDynamicCursor } from '../../hooks/cursor/dynamic-cursor.hook';
import { determineInteraction, getCursorClass } from '../../lib/utils/canvas-utils';
import { ITEM_STATUS } from '../../types/item.type';
import KonvaStage from './config/konva-stage';
import { CanvasSnapLines } from './config/snap-lines';
import { ShapesLayer } from './shapes/shapes-layer';
import { ShapeTooltip } from './shapes/tooltip';

const CanvasEditor = ({ propertiesPanelRef }: { propertiesPanelRef: React.RefObject<HTMLDivElement> }) => {
	const {
		containerRef,
		handleCanvasClick,
		handleMouseMove,
		handleSelectShape,
		handleDragEnd,
		handleTransformEnd,
		handleDragStart,
		handleDragMove,
		hoverPosition,
		transformerRef,
		stageRef,
		handleMouseLeave,
		scale,
		canvasHeight,
		canvasWidth,
		editableAreaWidth,
		editableAreaHeight,
		padding,
		handleTransformStart,
		handleTransform,
	} = useCanvasEditor();
	const items = useAtomValue(itemsAtom);
	const movingItem = items.find((item) => item.isDragging);
	const nextCanvasElement = useAtomValue(nextElementAtom);

	const visibleItems = useMemo(() => items.filter((item) => item.status !== ITEM_STATUS.DELETED), [items]);

	const { selectionBox, handleMouseDown, handleMouseMoveSelectionBox, handleMouseUp } = useSelectionBox(
		visibleItems,
		movingItem !== undefined,
		canvasWidth - padding * 2,
		canvasHeight - padding * 2,
		padding,
		scale,
	);

	useDynamicCursor({ interaction: determineInteraction(movingItem !== undefined, nextCanvasElement, !!selectionBox) });
	const { hoveredShape, handleShapeMouseEnter, handleShapeMouseLeave, clearHoveredShape } = useShapeHover();
	useClickOutside({ propertiesPanelRef, containerRef });
	const cursorClass = getCursorClass(!!selectionBox, nextCanvasElement);

	return (
		<div className="flex h-full w-full flex-col justify-center">
			<div
				ref={containerRef}
				role="application"
				tabIndex={0}
				className={`relative ${cursorClass} flex h-full w-full items-center justify-center focus:outline-none`}
			>
				<div tabIndex={-1} className="relative focus:outline-none" style={{ width: canvasWidth, height: canvasHeight }}>
					<OverlayContainer canvasProps={{ width: canvasWidth, height: canvasHeight }} padding={padding}>
						<HTMLOverlay
							scale={scale}
							containerSize={{
								width: canvasWidth - padding * 2,
								height: canvasHeight - padding * 2,
							}}
						/>
					</OverlayContainer>
					<KonvaStage
						width={canvasWidth}
						height={canvasHeight}
						ref={stageRef}
						onMouseLeave={handleMouseLeave}
						onStageClick={selectionBox ? () => {} : handleCanvasClick}
						onMouseMove={(e) => {
							handleMouseMove(e);
							handleMouseMoveSelectionBox(e);
						}}
						onMouseDown={handleMouseDown}
						onMouseUp={handleMouseUp}
					>
						<ShapesLayer
							clearHoveredShape={clearHoveredShape}
							onSelectShape={selectionBox ? () => {} : handleSelectShape}
							onDragEnd={handleDragEnd}
							onTransformEnd={handleTransformEnd}
							onDragStart={handleDragStart}
							onDragMove={({ event }) => handleDragMove(event)}
							onTransformStart={handleTransformStart}
							transformerRef={transformerRef}
							scale={scale}
							hoverPosition={hoverPosition}
							canvasWidth={canvasWidth}
							canvasHeight={canvasHeight}
							handleTransform={handleTransform}
							onShapeMouseEnter={handleShapeMouseEnter}
							onShapeMouseLeave={handleShapeMouseLeave}
							selectionBox={selectionBox}
							currentlyHoveredItem={hoveredShape.item}
							padding={padding}
							editableAreaWidth={editableAreaWidth}
							editableAreaHeight={editableAreaHeight}
						/>
						<CanvasSnapLines width={canvasWidth - padding * 2} height={canvasHeight - padding * 2} padding={padding} />
					</KonvaStage>
					<ShapeTooltip
						visible={!!hoveredShape.item && !movingItem && !nextCanvasElement && !selectionBox}
						x={hoveredShape.x}
						y={hoveredShape.y}
						name={hoveredShape.item?.name ?? ''}
						width={hoveredShape.item?.width ?? 0}
						height={hoveredShape.item?.height ?? 0}
						containerWidth={canvasWidth}
					/>
				</div>
			</div>
		</div>
	);
};

export const CanvasEditorWrapper = memo(({ propertiesPanelRef }: { propertiesPanelRef: React.RefObject<HTMLDivElement> }) => (
	<ErrorBoundary>
		<CanvasEditor propertiesPanelRef={propertiesPanelRef} />
	</ErrorBoundary>
));
