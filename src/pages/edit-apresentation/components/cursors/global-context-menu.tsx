import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import { contextMenuAtom } from '../../states/context-menu/context-menu.state';

export function GlobalContextMenu() {
	const [menuState, setMenuState] = useAtom(contextMenuAtom);
	const { visible, x, y, items } = menuState;

	const menuRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		function handleClickOutside(e: MouseEvent) {
			if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
				setMenuState((prev) => ({ ...prev, visible: false }));
			}
		}
		if (visible) {
			document.addEventListener('mousedown', handleClickOutside);
		}
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [visible, setMenuState]);

	return (
		<AnimatePresence>
			{visible && (
				<motion.div
					ref={menuRef}
					initial={{ opacity: 0, scale: 0.95, y: -4 }}
					animate={{ opacity: 1, scale: 1, y: 0 }}
					exit={{ opacity: 0, scale: 0.95, y: -2 }}
					transition={{ type: 'spring', stiffness: 450, damping: 30 }}
					className="absolute z-50 min-w-[200px] rounded-xl border border-border/40 bg-background/95 p-2 shadow-lg backdrop-blur-md dark:bg-zinc-900/95 dark:shadow-black/40"
					style={{ top: y, left: x }}
				>
					<div className="flex flex-col gap-1">
						{items.map((item, idx) => (
							<motion.div
								key={idx}
								className={`group flex items-center justify-between gap-3 rounded-md px-3.5 py-2 text-sm font-medium transition-all ${
									item.disabled
										? 'cursor-not-allowed opacity-50'
										: 'cursor-pointer hover:bg-primary/15 hover:text-primary active:bg-primary/20'
								}`}
								whileHover={item.disabled ? {} : { x: 3, backgroundColor: 'rgba(var(--primary), 0.12)' }}
								whileTap={item.disabled ? {} : { scale: 0.98 }}
								onClick={() => {
									if (!item.disabled) {
										item.onClick();
										setMenuState((prev) => ({ ...prev, visible: false }));
									}
								}}
							>
								<span className="relative">{item.label}</span>
								{item.icon && (
									<span
										className={`transition-colors ${
											item.disabled ? 'text-muted-foreground/50' : 'text-muted-foreground group-hover:text-primary/90'
										}`}
									>
										{item.icon}
									</span>
								)}
							</motion.div>
						))}
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
